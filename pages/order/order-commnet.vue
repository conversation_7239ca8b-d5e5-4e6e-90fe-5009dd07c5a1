<template>
  <view class="">
    <!-- 商品区 -->
    <view class="padding margin-lr margin-tb-sm bg-main border-radius">
      <view class="goods-area" v-for="(item, index) in orderItemList" :key="index">
        <view class="dflex">
          <view class="img">
            <image :src="item.goodsImage"></image>
          </view>
          <view class="margin-left-sm">
            <text class="clamp-2">{{ item.goodsName }} {{ item.goodsSkuName }}</text>
            <view class="ft-dark fs-xs padding-top-xs">
              <text class="margin-right">× {{ item.quantity }}</text>
              {{ item.goodsSkuName || '&nbsp;&nbsp;' }}
            </view>
            <view class="margin-top-sm">
              <text class="price">{{ item.price }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 评分 -->
    <view class="evaluate-kps">
      <view class="padding margin-lr margin-tb-sm bg-main border-radius dflex-b">
        <view>
          <text>总体评分</text>
          <text class="margin-left ft-base fs-xs">{{ postData.review_type }}</text>
        </view>
        <use-rate @change="rateChange" value="5"></use-rate>
      </view>
    </view>

    <!-- 评价区 -->
    <view class="evaluate-area">
      <view class="padding margin-lr margin-tb-sm bg-main border-radius">
        <!-- 评价内容 -->
        <textarea class="ft-black w-full fs-sm" v-model="postData.review_content"
                  placeholder="请输入评价内容"></textarea>

        <!-- 上传图片 -->
        <use-upload class="pos-r" @upload="uploadImgs"></use-upload>
      </view>
    </view>

    <!-- 是否匿名评价 -->
    <!--<view>-->
    <!--  <view class="padding margin-lr margin-tb-sm bg-main border-radius dflex-b">-->
    <!--    <text>提交评价的图片{{ anonymity ? '不可见' : '可见' }}</text>-->
    <!--    <view>-->
    <!--      <text class="ft-dark">匿名</text>-->
    <!--      <switch color="#FF6A6C" @change="switchChange"/>-->
    <!--    </view>-->
    <!--  </view>-->
    <!--</view>-->

    <!-- 提交操作 -->
    <view class="padding w-full margin-top">
      <view class="dflex-b border-radius-big">
        <view class="tac padding-tb-sm flex1 bg-base" @click="submit">提交评价</view>
      </view>
    </view>

    <!-- 版权 -->
    <use-copyright></use-copyright>
  </view>
</template>

<script setup>
import {ref, reactive} from 'vue'
import {onLoad} from '@dcloudio/uni-app'
import {getOrderInfoData, postOrderCommentData} from '/common/api/order'

const orderId = ref()
const orderItemList = ref([])
const postData = reactive({
  score: 5,
  content: '',
  status: 1,
  images: []
})

const rateChange = (index) => {
  postData.score = index
}

const uploadImgs = (options) => {
  let imgs = [];

  options.forEach((_) => {
    imgs.push(_.url);
  });

  if (imgs.length > 0) {
    postData.images.push(...imgs)
  }
}

const submit = async () => {
  const {score, content, images} = postData
  if (!score) {
    uni.showToast({
      title: '请选择评分',
      icon: 'none'
    })
    return
  }
  if (!content) {
    uni.showToast({
      title: '请输入评价内容',
      icon: 'none'
    })
    return
  }
  try {
    const res = await postOrderCommentData({
      score,
      content,
      images: images.join(','),
      orderId: orderId.value
    })
    uni.showToast({
      title: '评价成功',
    })
    uni.navigateBack()
  } catch (e) {
    uni.showToast({
      title: e.data.msg,
      icon: 'none'
    })
  }
}

onLoad((params) => {
  orderId.value = params.id
  getOrderInfoData(orderId.value).then(data => {
    orderItemList.value = data.orderItemList
  })

})
</script>

<style lang="scss">
page {
  background: $page-color-base;
}

/* 商品区 */
.goods-area {
  &:last-child {
    margin-bottom: 0;
  }

  image {
    width: 180rpx;
    height: 180rpx;
  }
}
</style>
