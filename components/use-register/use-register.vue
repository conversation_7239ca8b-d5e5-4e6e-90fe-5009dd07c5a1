<template>
  <uni-popup ref="popup" type="bottom">
    <view class="container bg-main pos-r">
      <view class="padding-xl dflex-c dflex-flow-c">
        <view class="portrait-box margin-bottom">
          <image class="headimg border-radius-c"
                 src="/static/images/logo-mini.png"></image>
        </view>

        <view class="w-full dflex padding-bottom-sm">
          <view class="iconfont iconshouji margin-right"></view>
          <view class="flex1 dflex">
            <input class="border-line padding-sm flex1" type="number" maxlength="11"
                   v-model="form.mobile" placeholder="请输入手机号"/>
          </view>
        </view>

        <view class="w-full dflex padding-bottom-sm">
          <view class="iconfont iconmima margin-right"></view>
          <input class="border-line padding-sm flex1" type="password" maxlength="20"
                 v-model="form.password" placeholder="请输入密码"/>
        </view>

        <view class="w-full dflex padding-bottom-sm">
          <view class="iconfont iconmima margin-right"></view>
          <input class="border-line padding-sm flex1" type="password" maxlength="20"
                 v-model="form.confirmPassword" placeholder="请确认密码"/>
        </view>

        <view class="w-full margin-top-xl">
          <view class="dflex-b border-radius-lg">
            <view class="tac padding-tb-sm flex1 bg-mint text-white fs" @click="doRegister">注册</view>
          </view>
        </view>
        <view class="w-full margin-top-xl">
          <view class="dflex-b border-radius-lg">
            <view class="tac padding-tb-sm flex1 bg-mint-light fs" @click="$refs.popup.close()">取消</view>
          </view>
        </view>
      </view>

      <!-- 用云版权 -->
      <use-copyright class="pos-f w-full" style="bottom: -30rpx"></use-copyright>
    </view>
  </uni-popup>
</template>

<script setup>
import {ref, reactive, defineExpose} from 'vue'
import {register} from "@/common/api"

const popup = ref(null)

const show = () => {
  popup.value.open()
}
const form = reactive({
  mobile: '',
  password: '',
  confirmPassword: ''
})

const doRegister = async () => {
  if (!form.mobile) {
    uni.showToast({title: '请输入手机号', icon: 'none'})
    return;
  }
  if (!/(^1[0-9][0-9]{9}$)/.test(form.mobile)) {
    uni.showToast({title: '请输入正确的手机号码', icon: 'none'})
    return;
  }

  if (!form.password) {
    uni.showToast({title: '请输入密码', icon: 'none'})
    return;
  }
  if (form.password.trim().length < 4) {
    uni.showToast({title: '密码长度不能小于4位', icon: 'none'})
    return;
  }
  if (form.password !== form.confirmPassword) {
    uni.showToast({title: '两次密码不一致', icon: 'none'})
    return;
  }
  uni.showLoading({
    title: '加载中'
  });
  await register({phone: form.mobile, password: form.password})
  uni.hideLoading()
  uni.showToast({title: '注册成功'})

  popup.value.close()
}

defineExpose({
  show
})
</script>

<style lang="scss">
page {
  background: #f4f4f4;
}

.container {
  padding-top: 5vh;
  width: 100vw;
  min-height: 100vh;
  overflow: hidden;
}

.portrait-box {
  image {
    width: 130rpx;
    height: 130rpx;
    border: 5rpx solid #fff;
  }
}
</style>
